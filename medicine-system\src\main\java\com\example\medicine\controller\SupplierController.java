package com.example.medicine.controller;

import com.example.medicine.common.Result;
import com.example.medicine.common.PageResult;
import com.example.medicine.entity.Supplier;
import com.example.medicine.service.SupplierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/api/supplier")
public class SupplierController {
    @Autowired
    private SupplierService supplierService;

    @PostMapping("/add")
    public Result<Supplier> addSupplier(@RequestBody Supplier supplier) {
        try {
            return Result.success(supplierService.addSupplier(supplier));
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    @PutMapping("/update/{id}")
    public Result<Supplier> updateSupplier(@PathVariable Long id, @RequestBody Supplier supplier) {
        try {
            return Result.success(supplierService.updateSupplier(id, supplier));
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/delete/{id}")
    public Result<Void> deleteSupplier(@PathVariable Long id) {
        try {
            supplierService.deleteSupplier(id);
            return Result.success(null);
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    @GetMapping("/get/{id}")
    public Result<Supplier> getSupplierById(@PathVariable Long id) {
        try {
            return Result.success(supplierService.getSupplierById(id));
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/list")
    public Result<PageResult<Supplier>> getSupplierList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "") String keyword) {
        try {
            List<Supplier> allSuppliers = supplierService.getAllSuppliers();

            // 如果有关键字搜索，进行过滤
            if (keyword != null && !keyword.trim().isEmpty()) {
                String searchKeyword = keyword.trim().toLowerCase();
                allSuppliers = allSuppliers.stream()
                    .filter(supplier ->
                        (supplier.getName() != null && supplier.getName().toLowerCase().contains(searchKeyword)) ||
                        (supplier.getContact() != null && supplier.getContact().toLowerCase().contains(searchKeyword)) ||
                        (supplier.getAddress() != null && supplier.getAddress().toLowerCase().contains(searchKeyword))
                    )
                    .collect(java.util.stream.Collectors.toList());
            }

            // 简单的分页逻辑
            int total = allSuppliers.size();
            int start = (page - 1) * size;
            int end = Math.min(start + size, total);

            List<Supplier> records = start < total ? allSuppliers.subList(start, end) : List.of();

            PageResult<Supplier> pageResult = new PageResult<>();
            pageResult.setRecords(records);
            pageResult.setTotal(total);
            pageResult.setCurrent(page);
            pageResult.setSize(size);

            return Result.success(pageResult);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/all")
    public Result<List<Supplier>> getAllSuppliersForSelect() {
        try {
            return Result.success(supplierService.getAllSuppliers());
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @PostMapping("/toggle-status/{id}")
    public Result<Void> toggleSupplierStatus(@PathVariable Long id, @RequestBody java.util.Map<String, Integer> request) {
        try {
            Integer status = request.get("status");
            Supplier supplier = supplierService.getSupplierById(id);
            if (supplier == null) {
                return Result.error("供应商不存在");
            }

            // 设置新状态
            supplier.setStatusFromNumber(status);
            supplierService.updateSupplier(id, supplier);

            return Result.success(null);
        } catch (Exception e) {
            return Result.error("状态更新失败: " + e.getMessage());
        }
    }
}