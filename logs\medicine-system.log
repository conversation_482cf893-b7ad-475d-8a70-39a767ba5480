2025-08-03 18:26:53.786  INFO 18712 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 18712 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 18:26:53.793 DEBUG 18712 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 18:26:53.794  INFO 18712 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 18:26:53.883  INFO 18712 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 18:26:53.883  INFO 18712 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 18:26:54.853  INFO 18712 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 18:26:54.974  INFO 18712 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 110 ms. Found 12 JPA repository interfaces.
2025-08-03 18:26:55.812  INFO 18712 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 18:26:55.824  INFO 18712 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 18:26:55.825  INFO 18712 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 18:26:55.919  INFO 18712 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 18:26:55.919  INFO 18712 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2035 ms
2025-08-03 18:26:56.182  INFO 18712 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 18:26:56.261  INFO 18712 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 18:26:56.500  INFO 18712 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 18:26:56.656  INFO 18712 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 18:26:57.336  INFO 18712 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 18:26:57.360  INFO 18712 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 18:26:58.463  INFO 18712 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 18:26:58.476  INFO 18712 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 18:26:58.539  WARN 18712 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 18:26:59.576  WARN 18712 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: a0921846-b5b3-4e42-8fcf-0c1a17e30627

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 18:26:59.745  INFO 18712 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@500edf46, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@690e882a, org.springframework.security.web.context.SecurityContextPersistenceFilter@412cf680, org.springframework.security.web.header.HeaderWriterFilter@25158332, org.springframework.web.filter.CorsFilter@2fe9d75a, org.springframework.security.web.authentication.logout.LogoutFilter@1854deb1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4157de22, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@371fb93, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1eb45e28, org.springframework.security.web.session.SessionManagementFilter@54f3b3d, org.springframework.security.web.access.ExceptionTranslationFilter@3b1727cd, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@23706aee]
2025-08-03 18:27:00.247  INFO 18712 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 18:27:00.292  INFO 18712 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 18:27:00.305  INFO 18712 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 7.052 seconds (JVM running for 12.378)
2025-08-03 18:27:33.373  INFO 18712 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 18:27:33.373  INFO 18712 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 18:27:33.375  INFO 18712 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-03 18:35:27.404  INFO 18712 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 18:35:27.430  INFO 18712 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 18:35:27.454  INFO 18712 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 18:35:46.991  INFO 3240 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 3240 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 18:35:46.993 DEBUG 3240 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 18:35:46.994  INFO 3240 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 18:35:47.086  INFO 3240 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 18:35:47.087  INFO 3240 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 18:35:49.276  INFO 3240 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 18:35:49.459  INFO 3240 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 170 ms. Found 12 JPA repository interfaces.
2025-08-03 18:35:50.450  INFO 3240 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 18:35:50.467  INFO 3240 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 18:35:50.468  INFO 3240 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 18:35:50.581  INFO 3240 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 18:35:50.582  INFO 3240 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3494 ms
2025-08-03 18:35:50.908  INFO 3240 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 18:35:50.979  INFO 3240 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 18:35:51.236  INFO 3240 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 18:35:51.387  INFO 3240 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 18:35:51.938  INFO 3240 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 18:35:51.955  INFO 3240 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 18:35:53.182  INFO 3240 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 18:35:53.195  INFO 3240 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 18:35:53.262  WARN 3240 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 18:35:54.520  WARN 3240 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 91625828-b55e-4b09-ae65-534986213e6a

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 18:35:54.676  INFO 3240 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@46640289, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3ceab1b2, org.springframework.security.web.context.SecurityContextPersistenceFilter@22bf185c, org.springframework.security.web.header.HeaderWriterFilter@31f681d, org.springframework.web.filter.CorsFilter@683289b1, org.springframework.security.web.authentication.logout.LogoutFilter@2b8e77ed, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@41e4c0fd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@21e45631, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2c080550, org.springframework.security.web.session.SessionManagementFilter@1e95eaed, org.springframework.security.web.access.ExceptionTranslationFilter@618b1932, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7854c9bf]
2025-08-03 18:35:55.306  INFO 3240 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 18:35:55.383  INFO 3240 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 18:35:55.403  INFO 3240 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 8.966 seconds (JVM running for 10.739)
2025-08-03 18:35:56.201  INFO 3240 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 18:35:56.201  INFO 3240 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 18:35:56.204  INFO 3240 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-03 18:41:57.990  INFO 3240 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 18:41:57.998  INFO 3240 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 18:41:58.015  INFO 3240 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 18:42:14.369  INFO 19112 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 19112 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 18:42:14.370 DEBUG 19112 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 18:42:14.371  INFO 19112 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 18:42:14.452  INFO 19112 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 18:42:14.452  INFO 19112 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 18:42:16.654  INFO 19112 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 18:42:16.831  INFO 19112 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 162 ms. Found 12 JPA repository interfaces.
2025-08-03 18:42:17.754  INFO 19112 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 18:42:17.770  INFO 19112 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 18:42:17.770  INFO 19112 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 18:42:17.889  INFO 19112 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 18:42:17.889  INFO 19112 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3436 ms
2025-08-03 18:42:18.166  INFO 19112 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 18:42:18.246  INFO 19112 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 18:42:18.466  INFO 19112 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 18:42:18.604  INFO 19112 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 18:42:19.146  INFO 19112 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 18:42:19.163  INFO 19112 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 18:42:20.149  INFO 19112 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 18:42:20.160  INFO 19112 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 18:42:20.238  WARN 19112 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 18:42:21.201  WARN 19112 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 7c5f1331-166b-41ea-9b28-3e3e4f9fdb6f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 18:42:21.336  INFO 19112 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1fff10d5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@16b1cef7, org.springframework.security.web.context.SecurityContextPersistenceFilter@13226a50, org.springframework.security.web.header.HeaderWriterFilter@331528b7, org.springframework.web.filter.CorsFilter@b624ca0, org.springframework.security.web.authentication.logout.LogoutFilter@7040fac, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@ffd75a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3eee1dc3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@69d7bbe6, org.springframework.security.web.session.SessionManagementFilter@750f5f20, org.springframework.security.web.access.ExceptionTranslationFilter@408fc75c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7a708ccd]
2025-08-03 18:42:21.756  INFO 19112 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 18:42:21.798  INFO 19112 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 18:42:21.809  INFO 19112 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 7.974 seconds (JVM running for 9.409)
