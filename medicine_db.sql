/*
 Navicat Premium Dump SQL

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 90001 (9.0.1)
 Source Host           : localhost:3306
 Source Schema         : medicine_db

 Target Server Type    : MySQL
 Target Server Version : 90001 (9.0.1)
 File Encoding         : 65001

 Date: 02/08/2025 20:23:33
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for customer
-- ----------------------------
DROP TABLE IF EXISTS `customer`;
CREATE TABLE `customer`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `contact` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of customer
-- ----------------------------
INSERT INTO `customer` VALUES (1, '张三', '13800000001', '北京市海淀区知春路1号');
INSERT INTO `customer` VALUES (2, '李四', '13800000002', '上海市徐汇区漕溪北路2号');
INSERT INTO `customer` VALUES (3, '王五', '13800000003', '广州市天河区体育西路3号');
INSERT INTO `customer` VALUES (4, '赵六', '13800000004', '深圳市南山区科技园4号');
INSERT INTO `customer` VALUES (5, '钱七', '13800000005', '杭州市西湖区文三路5号');
INSERT INTO `customer` VALUES (6, '孙八', '13800000006', '成都市锦江区春熙路6号');
INSERT INTO `customer` VALUES (7, '周九', '13800000007', '重庆市江北区观音桥7号');
INSERT INTO `customer` VALUES (8, '吴十', '13800000008', '南京市鼓楼区中山路8号');
INSERT INTO `customer` VALUES (9, '郑十一', '13800000009', '武汉市洪山区珞喻路9号');
INSERT INTO `customer` VALUES (10, '冯十二', '13800000010', '西安市雁塔区小寨路10号');

-- ----------------------------
-- Table structure for inventory
-- ----------------------------
DROP TABLE IF EXISTS `inventory`;
CREATE TABLE `inventory`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `medicine_id` bigint NOT NULL,
  `warehouse_id` bigint NULL DEFAULT NULL,
  `quantity` int NOT NULL,
  `last_update` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `medicine_id`(`medicine_id` ASC) USING BTREE,
  CONSTRAINT `inventory_ibfk_1` FOREIGN KEY (`medicine_id`) REFERENCES `medicine` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of inventory
-- ----------------------------
INSERT INTO `inventory` VALUES (1, 1, 1, 531, '2025-08-01 21:20:01');
INSERT INTO `inventory` VALUES (2, 2, 1, 300, '2024-04-11 09:00:00');
INSERT INTO `inventory` VALUES (3, 3, 1, 400, '2024-04-11 09:00:00');
INSERT INTO `inventory` VALUES (4, 4, 1, 200, '2024-04-11 09:00:00');
INSERT INTO `inventory` VALUES (5, 5, 1, 150, '2024-04-11 09:00:00');
INSERT INTO `inventory` VALUES (6, 6, 1, 350, '2024-04-11 09:00:00');
INSERT INTO `inventory` VALUES (7, 7, 1, 120, '2024-04-11 09:00:00');
INSERT INTO `inventory` VALUES (8, 8, 1, 180, '2024-04-11 09:00:00');
INSERT INTO `inventory` VALUES (9, 9, 1, 90, '2024-04-11 09:00:00');
INSERT INTO `inventory` VALUES (10, 10, 1, 210, '2024-04-11 09:00:00');
INSERT INTO `inventory` VALUES (11, 11, 1, 280, '2025-07-31 20:00:00');
INSERT INTO `inventory` VALUES (12, 12, 1, 160, '2025-07-31 20:00:00');
INSERT INTO `inventory` VALUES (13, 13, 1, 95, '2025-07-31 20:00:00');
INSERT INTO `inventory` VALUES (14, 14, 1, 320, '2025-07-31 20:00:00');
INSERT INTO `inventory` VALUES (15, 15, 1, 240, '2025-07-31 20:00:00');
INSERT INTO `inventory` VALUES (16, 16, 1, 180, '2025-07-31 20:00:00');
INSERT INTO `inventory` VALUES (17, 17, 1, 150, '2025-07-31 20:00:00');
INSERT INTO `inventory` VALUES (18, 18, 1, 200, '2025-07-31 20:00:00');
INSERT INTO `inventory` VALUES (19, 19, 1, 110, '2025-07-31 20:00:00');
INSERT INTO `inventory` VALUES (20, 20, 1, 260, '2025-07-31 20:00:00');

-- ----------------------------
-- Table structure for inventory_record
-- ----------------------------
DROP TABLE IF EXISTS `inventory_record`;
CREATE TABLE `inventory_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `after_stock` int NULL DEFAULT NULL,
  `before_stock` int NULL DEFAULT NULL,
  `create_time` datetime(6) NULL DEFAULT NULL,
  `medicine_id` bigint NULL DEFAULT NULL,
  `medicine_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `operator_id` bigint NULL DEFAULT NULL,
  `operator_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `quantity` int NULL DEFAULT NULL,
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of inventory_record
-- ----------------------------
INSERT INTO `inventory_record` VALUES (1, 530, 520, '2025-08-01 20:47:33.409000', 1, '阿莫西林胶囊', 1, '系统管理员', 10, '测试入库操作', 'IN');
INSERT INTO `inventory_record` VALUES (2, 531, 530, '2025-08-01 21:03:02.030000', 1, '阿莫西林胶囊', 1, '系统管理员', 1, '测试入库功能', 'IN');
INSERT INTO `inventory_record` VALUES (3, 532, 531, '2025-08-01 21:11:01.390000', 1, '阿莫西林胶囊', 1, '系统管理员', 1, '测试入库功能', 'IN');
INSERT INTO `inventory_record` VALUES (4, 531, 532, '2025-08-01 21:20:01.335000', 1, '阿莫西林胶囊', 1, '系统管理员', 1, '测试出库功能', 'OUT');

-- ----------------------------
-- Table structure for log
-- ----------------------------
DROP TABLE IF EXISTS `log`;
CREATE TABLE `log`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NULL DEFAULT NULL,
  `action` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `timestamp` datetime NULL DEFAULT NULL,
  `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of log
-- ----------------------------
INSERT INTO `log` VALUES (1, 1, '登录', '2024-04-01 09:00:00', '管理员登录系统');
INSERT INTO `log` VALUES (2, 2, '采购入库', '2024-04-02 10:00:00', '采购员完成采购单#1入库');
INSERT INTO `log` VALUES (3, 3, '销售开单', '2024-04-03 11:00:00', '销售员完成销售单#1');
INSERT INTO `log` VALUES (4, 4, '库存盘点', '2024-04-04 12:00:00', '仓库管理员盘点库存');
INSERT INTO `log` VALUES (5, 5, '报表导出', '2024-04-05 13:00:00', '财务导出销售报表');
INSERT INTO `log` VALUES (6, 6, '系统设置', '2024-04-06 14:00:00', '运营修改系统公告');
INSERT INTO `log` VALUES (7, 8, '药品录入', '2024-04-07 15:00:00', '医生录入新药品');
INSERT INTO `log` VALUES (8, 9, '库存调整', '2024-04-08 16:00:00', '药剂师调整药品库存');
INSERT INTO `log` VALUES (9, 10, '日志审计', '2024-04-09 17:00:00', '审计员查看操作日志');
INSERT INTO `log` VALUES (10, 1, '用户管理', '2024-04-10 18:00:00', '管理员添加新用户');

-- ----------------------------
-- Table structure for medicine
-- ----------------------------
DROP TABLE IF EXISTS `medicine`;
CREATE TABLE `medicine`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `category_id` bigint NULL DEFAULT NULL,
  `spec` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `batch_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `expire_date` date NULL DEFAULT NULL,
  `price` decimal(10, 2) NULL DEFAULT NULL,
  `stock` int NULL DEFAULT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_medicine_name_batch`(`name` ASC, `batch_no` ASC) USING BTREE,
  INDEX `fk_medicine_category`(`category_id` ASC) USING BTREE,
  CONSTRAINT `fk_medicine_category` FOREIGN KEY (`category_id`) REFERENCES `medicine_category` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of medicine
-- ----------------------------
INSERT INTO `medicine` VALUES (1, '阿莫西林胶囊', 1, '0.25g*24粒', 'AMX202401', '2025-12-31', 18.00, 501, 'ONSALE');
INSERT INTO `medicine` VALUES (2, '布洛芬片', 1, '0.2g*20片', 'BLF202402', '2026-06-30', 12.50, 300, 'OFFSALE');
INSERT INTO `medicine` VALUES (3, '维生素C片', 2, '100mg*60片', 'VC202403', '2025-09-30', 15.00, 400, 'ONSALE');
INSERT INTO `medicine` VALUES (4, '葡萄糖注射液', 3, '500ml*1瓶', 'PTT202404', '2025-03-31', 8.00, 200, 'ONSALE');
INSERT INTO `medicine` VALUES (5, '头孢克肟片', 1, '50mg*12片', 'TBK202405', '2026-01-31', 25.00, 150, 'ONSALE');
INSERT INTO `medicine` VALUES (6, '氯化钠注射液', 3, '250ml*1瓶', 'LHN202406', '2025-11-30', 5.00, 350, 'ONSALE');
INSERT INTO `medicine` VALUES (7, '地塞米松片', 2, '0.75mg*20片', 'DSM202407', '2026-04-30', 22.00, 120, 'ONSALE');
INSERT INTO `medicine` VALUES (8, '甲硝唑片', 1, '0.2g*20片', 'JXZ202408', '2025-08-31', 10.00, 180, 'ONSALE');
INSERT INTO `medicine` VALUES (9, '葡萄糖口服液', 3, '100ml*6瓶', 'PTTK202409', '2025-10-31', 20.00, 90, 'ONSALE');
INSERT INTO `medicine` VALUES (10, '复方感冒灵颗粒', 2, '10g*9袋', 'FFGML202410', '2026-02-28', 28.00, 210, 'ONSALE');
INSERT INTO `medicine` VALUES (11, '阿司匹林肠溶片', 2, '100mg*30片', 'ASP202411', '2026-08-31', 16.50, 280, 'ONSALE');
INSERT INTO `medicine` VALUES (12, '奥美拉唑胶囊', 8, '20mg*14粒', 'OML202412', '2025-12-31', 32.00, 160, 'ONSALE');
INSERT INTO `medicine` VALUES (13, '硝苯地平缓释片', 7, '30mg*7片', 'XBD202413', '2026-05-31', 45.00, 95, 'ONSALE');
INSERT INTO `medicine` VALUES (14, '复方甘草片', 9, '50片/瓶', 'FGGC202414', '2025-07-31', 8.50, 320, 'ONSALE');
INSERT INTO `medicine` VALUES (15, '盐酸二甲双胍片', 4, '0.5g*20片', 'YS202415', '2026-03-31', 18.80, 240, 'ONSALE');
INSERT INTO `medicine` VALUES (16, '氨茶碱片', 9, '0.1g*100片', 'ACJ202416', '2025-11-30', 12.00, 180, 'ONSALE');
INSERT INTO `medicine` VALUES (17, '维生素B族片', 6, '复合维生素*60片', 'VSB202417', '2026-01-31', 22.50, 150, 'ONSALE');
INSERT INTO `medicine` VALUES (18, '硫酸亚铁片', 6, '0.3g*50片', 'LSY202418', '2025-09-30', 14.00, 200, 'ONSALE');
INSERT INTO `medicine` VALUES (19, '利巴韦林颗粒', 5, '50mg*12袋', 'LBW202419', '2026-06-30', 28.50, 110, 'ONSALE');
INSERT INTO `medicine` VALUES (20, '蒙脱石散', 8, '3g*10袋', 'MTS202420', '2025-10-31', 19.80, 260, 'ONSALE');
INSERT INTO `medicine` VALUES (21, 'test', 4, 'test', 'test', '2025-08-01', 10.00, 1, 'ONSALE');
INSERT INTO `medicine` VALUES (22, '阿莫西林胶囊', 5, '0.25g*24粒', '20241201', '2025-08-15', 25.50, 100, 'ONSALE');

-- ----------------------------
-- Table structure for medicine_category
-- ----------------------------
DROP TABLE IF EXISTS `medicine_category`;
CREATE TABLE `medicine_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_category_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of medicine_category
-- ----------------------------
INSERT INTO `medicine_category` VALUES (1, '处方药', '需要医生处方才能购买的药品', '2025-07-31 19:28:41', '2025-07-31 19:28:41');
INSERT INTO `medicine_category` VALUES (2, '非处方药', '可以直接购买的非处方药品', '2025-07-31 19:28:41', '2025-07-31 19:28:41');
INSERT INTO `medicine_category` VALUES (3, '中成药', '中医药制剂', '2025-07-31 19:28:41', '2025-07-31 19:28:41');
INSERT INTO `medicine_category` VALUES (4, '西药', '化学合成药物', '2025-07-31 19:28:41', '2025-07-31 19:28:41');
INSERT INTO `medicine_category` VALUES (5, '抗生素', '抗菌消炎药物', '2025-07-31 19:28:41', '2025-07-31 19:28:41');
INSERT INTO `medicine_category` VALUES (6, '维生素', '维生素类营养补充剂', '2025-07-31 19:28:41', '2025-07-31 19:28:41');
INSERT INTO `medicine_category` VALUES (7, '心血管药', '治疗心血管疾病的药物', '2025-07-31 19:28:41', '2025-07-31 19:28:41');
INSERT INTO `medicine_category` VALUES (8, '消化系统药', '治疗消化系统疾病的药物', '2025-07-31 19:28:41', '2025-07-31 19:28:41');
INSERT INTO `medicine_category` VALUES (9, '呼吸系统药', '治疗呼吸系统疾病的药物', '2025-07-31 19:28:41', '2025-07-31 19:28:41');
INSERT INTO `medicine_category` VALUES (10, '神经系统药', '治疗神经系统疾病的药物', '2025-07-31 19:28:41', '2025-07-31 19:28:41');

-- ----------------------------
-- Table structure for purchase
-- ----------------------------
DROP TABLE IF EXISTS `purchase`;
CREATE TABLE `purchase`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `supplier_id` bigint NULL DEFAULT NULL,
  `medicine_id` bigint NULL DEFAULT NULL,
  `quantity` int NULL DEFAULT NULL,
  `price` decimal(10, 2) NULL DEFAULT NULL,
  `purchase_date` datetime NULL DEFAULT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FKru5ru4j1f33sl9vo1rtxhq5lp`(`medicine_id` ASC) USING BTREE,
  INDEX `FK8omm6fki86s9oqk0o9s6w43h5`(`supplier_id` ASC) USING BTREE,
  CONSTRAINT `FK8omm6fki86s9oqk0o9s6w43h5` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKru5ru4j1f33sl9vo1rtxhq5lp` FOREIGN KEY (`medicine_id`) REFERENCES `medicine` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of purchase
-- ----------------------------
INSERT INTO `purchase` VALUES (1, 1, 1, 100, 17.00, '2024-03-01 10:00:00', 'COMPLETED');
INSERT INTO `purchase` VALUES (2, 2, 2, 80, 11.50, '2024-03-02 11:00:00', 'COMPLETED');
INSERT INTO `purchase` VALUES (3, 3, 3, 120, 14.00, '2024-03-03 12:00:00', 'COMPLETED');
INSERT INTO `purchase` VALUES (4, 4, 4, 60, 7.50, '2024-03-04 13:00:00', 'APPROVED');
INSERT INTO `purchase` VALUES (5, 5, 5, 50, 24.00, '2024-03-05 14:00:00', 'APPROVED');
INSERT INTO `purchase` VALUES (6, 6, 6, 90, 4.50, '2024-03-06 15:00:00', 'APPROVED');
INSERT INTO `purchase` VALUES (7, 7, 7, 40, 21.00, '2024-03-07 16:00:00', 'APPROVED');
INSERT INTO `purchase` VALUES (8, 8, 8, 70, 9.00, '2024-03-08 17:00:00', 'APPROVED');
INSERT INTO `purchase` VALUES (9, 9, 9, 30, 19.00, '2024-03-09 18:00:00', 'APPROVED');
INSERT INTO `purchase` VALUES (10, 10, 10, 100, 27.00, '2024-03-10 19:00:00', 'APPROVED');

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `role_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `permissions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `role_name`(`role_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of role
-- ----------------------------
INSERT INTO `role` VALUES (1, '管理员', 'ALL');
INSERT INTO `role` VALUES (2, '采购员', 'purchase,inventory,supplier');
INSERT INTO `role` VALUES (3, '销售员', 'sale,customer');
INSERT INTO `role` VALUES (4, '仓库管理员', 'inventory,medicine');
INSERT INTO `role` VALUES (5, '财务', 'report');
INSERT INTO `role` VALUES (6, '运营', 'report,setting');
INSERT INTO `role` VALUES (7, '访客', '');
INSERT INTO `role` VALUES (8, '医生', 'medicine,customer');
INSERT INTO `role` VALUES (9, '药剂师', 'medicine,inventory');
INSERT INTO `role` VALUES (10, '系统审计', 'log,report');

-- ----------------------------
-- Table structure for sale
-- ----------------------------
DROP TABLE IF EXISTS `sale`;
CREATE TABLE `sale`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `customer_id` bigint NULL DEFAULT NULL,
  `medicine_id` bigint NULL DEFAULT NULL,
  `quantity` int NULL DEFAULT NULL,
  `price` decimal(10, 2) NULL DEFAULT NULL,
  `sale_date` datetime NULL DEFAULT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sale
-- ----------------------------
INSERT INTO `sale` VALUES (1, 1, 1, 10, 18.00, '2024-04-01 10:00:00', 'COMPLETED');
INSERT INTO `sale` VALUES (2, 2, 2, 8, 12.50, '2024-04-02 11:00:00', 'COMPLETED');
INSERT INTO `sale` VALUES (3, 3, 3, 12, 15.00, '2024-04-03 12:00:00', 'COMPLETED');
INSERT INTO `sale` VALUES (4, 4, 4, 6, 8.00, '2024-04-04 13:00:00', 'COMPLETED');
INSERT INTO `sale` VALUES (5, 5, 5, 5, 25.00, '2024-04-05 14:00:00', 'COMPLETED');
INSERT INTO `sale` VALUES (6, 6, 6, 9, 5.00, '2024-04-06 15:00:00', 'COMPLETED');
INSERT INTO `sale` VALUES (7, 7, 7, 4, 22.00, '2024-04-07 16:00:00', 'COMPLETED');
INSERT INTO `sale` VALUES (8, 8, 8, 7, 10.00, '2024-04-08 17:00:00', 'COMPLETED');
INSERT INTO `sale` VALUES (9, 9, 9, 3, 20.00, '2024-04-09 18:00:00', 'COMPLETED');
INSERT INTO `sale` VALUES (10, 10, 10, 10, 28.00, '2024-04-10 19:00:00', 'COMPLETED');

-- ----------------------------
-- Table structure for setting
-- ----------------------------
DROP TABLE IF EXISTS `setting`;
CREATE TABLE `setting`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `param_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `param_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `param_key`(`param_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of setting
-- ----------------------------
INSERT INTO `setting` VALUES (1, 'system_name', '医药管理系统', '系统名称');
INSERT INTO `setting` VALUES (2, 'login_captcha', 'true', '登录是否启用验证码');
INSERT INTO `setting` VALUES (3, 'stock_warning', '50', '库存预警阈值');
INSERT INTO `setting` VALUES (4, 'report_export_format', 'xlsx', '报表导出格式');
INSERT INTO `setting` VALUES (5, 'theme', 'default', '系统主题');
INSERT INTO `setting` VALUES (6, 'max_login_attempts', '5', '最大登录失败次数');
INSERT INTO `setting` VALUES (7, 'backup_cycle', '7', '数据备份周期（天）');
INSERT INTO `setting` VALUES (8, 'enable_log', 'true', '是否开启操作日志');
INSERT INTO `setting` VALUES (9, 'default_role', '访客', '新用户默认角色');
INSERT INTO `setting` VALUES (10, 'notice', '欢迎使用本系统', '系统公告');

-- ----------------------------
-- Table structure for supplier
-- ----------------------------
DROP TABLE IF EXISTS `supplier`;
CREATE TABLE `supplier`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `contact` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'ACTIVE',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of supplier
-- ----------------------------
INSERT INTO `supplier` VALUES (1, '国药集团', '张经理', '010-88888888', '<EMAIL>', '北京市朝阳区建国路88号', '国内最大的医药流通企业', 'ACTIVE', '2025-01-01 10:00:00');
INSERT INTO `supplier` VALUES (2, '华润医药', '李总监', '021-66666666', '<EMAIL>', '上海市浦东新区世纪大道100号', '华润集团旗下医药企业', 'ACTIVE', '2025-01-01 10:00:00');
INSERT INTO `supplier` VALUES (3, '上药控股', '王主任', '021-77777777', '<EMAIL>', '上海市静安区南京西路200号', '上海医药集团控股公司', 'ACTIVE', '2025-01-01 10:00:00');
INSERT INTO `supplier` VALUES (4, '九州通', '陈部长', '027-88889999', '<EMAIL>', '武汉市武昌区中南路300号', '全国性医药流通企业', 'ACTIVE', '2025-01-01 10:00:00');
INSERT INTO `supplier` VALUES (5, '云南白药', '赵经理', '0871-12345678', '<EMAIL>', '昆明市五华区人民中路400号', '知名中药制药企业', 'ACTIVE', '2025-01-01 10:00:00');
INSERT INTO `supplier` VALUES (6, '哈药集团', '刘代表', '0451-87654321', '<EMAIL>', '哈尔滨市南岗区中山路500号', '东北地区大型制药企业', 'INACTIVE', '2025-01-01 10:00:00');
INSERT INTO `supplier` VALUES (7, '太极集团', '023-23456789', '重庆市渝中区解放碑600号', 'ACTIVE', '2025-01-01 10:00:00');
INSERT INTO `supplier` VALUES (8, '同仁堂', '010-99999999', '北京市东城区东华门700号', 'ACTIVE', '2025-01-01 10:00:00');
INSERT INTO `supplier` VALUES (9, '石药集团', '0311-88887777', '石家庄市桥西区中华大街800号', 'ACTIVE', '2025-01-01 10:00:00');
INSERT INTO `supplier` VALUES (10, '以岭药业', '0311-66665555', '石家庄市裕华区槐安路900号', 'ACTIVE', '2025-01-01 10:00:00');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `role_id` bigint NULL DEFAULT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `department` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `last_login_time` datetime(6) NULL DEFAULT NULL,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `position` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `real_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `user_settings` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 'admin', '$2a$10$123456', 1, 'ACTIVE', '2024-01-01 09:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `user` VALUES (2, 'buyer1', '$2a$10$buyer123', 2, 'ACTIVE', '2024-01-02 10:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `user` VALUES (3, 'seller1', '$2a$10$seller123', 3, 'ACTIVE', '2024-01-03 11:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `user` VALUES (4, 'store1', '$2a$10$store123', 4, 'ACTIVE', '2024-01-04 12:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `user` VALUES (5, 'finance1', '$2a$10$finance123', 5, 'ACTIVE', '2024-01-05 13:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `user` VALUES (6, 'op1', '$2a$10$op123', 6, 'ACTIVE', '2024-01-06 14:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `user` VALUES (7, 'guest1', '$2a$10$guest123', 7, 'INACTIVE', '2024-01-07 15:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `user` VALUES (8, 'doctor1', '$2a$10$doctor123', 8, 'ACTIVE', '2024-01-08 16:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `user` VALUES (9, 'pharma1', '$2a$10$pharma123', 9, 'ACTIVE', '2024-01-09 17:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `user` VALUES (10, 'audit1', '$2a$10$audit123', 10, 'ACTIVE', '2024-01-10 18:00:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `user` VALUES (13, 'hlh', '123456HLh.', 1, 'active', '2025-07-31 18:57:11', NULL, NULL, NULL, NULL, NULL, NULL, 'haha', '{\"theme\":\"light\",\"language\":\"zh-CN\",\"notifications\":{\"email\":true,\"system\":true,\"lowStock\":true,\"expiring\":true},\"layout\":{\"sidebarCollapsed\":false,\"showBreadcrumb\":true,\"showTabs\":true}}');

SET FOREIGN_KEY_CHECKS = 1;
